<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "E8C7BF2D-6664-402E-AC90-6C89E94D046C"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "B4F619B3-720E-4990-8278-5A5CA7A13D3F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.SymbolicBreakpoint">
         <BreakpointContent
            uuid = "DAEACA6A-8F0B-4C3F-881C-CE5502E2FDCC"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            symbolName = "UIViewAlertForUnsatisfiableConstraints"
            moduleName = "">
            <Locations>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DDA781B4-8E1E-41E0-8986-65ABAA5BBD21"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Components/UserAgreementConfirmPopupView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "235"
            endingLineNumber = "235"
            landmarkName = "UserAgreementConfirmPopupView"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
