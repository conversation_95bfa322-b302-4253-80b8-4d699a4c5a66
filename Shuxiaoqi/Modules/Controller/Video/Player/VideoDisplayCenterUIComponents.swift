//
//  VideoDisplayCenterUIComponents.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/26.
//

/**
 `VideoDisplayCenterUIComponents`
 
 视频展示中心页的 UI 组装器，只负责“视图”层面的创建与布局，不包含业务逻辑。
 主要职责：
 1. 构建自定义导航栏（含返回 / 搜索 / 更多按钮），支持外部根据 `showCustomNavBar`、`hideNavBackButton` 控制显示与隐藏。
 2. 提供垂直方向 `UIPageViewController` 作为短视频翻页核心容器。
 3. 对外暴露 `pageViewController`，方便 `VideoDisplayCenterViewController` 进行数据源、代理设置。
 
 设计要点：
 - 使用 SnapKit 进行自动布局，确保安全区适配。
 - 保持纯 UI 职责，任何事件响应由外部 ViewController 通过 target/action 设置。
 - 通过 `setupUI(in:showCustomNavBar:hideNavBackButton:needsTabBarOffset:...)` 统一注入依赖，可根据业务需求一次性配置。
 */
import UIKit
import SnapKit

// MARK: - 视频展示页UI组件
class VideoDisplayCenterUIComponents {
    // MARK: - UI Components
    let navBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    let backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_back_white"), for: .normal)
        return button
    }()
    
    let searchButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_search_white"), for: .normal)
        return button
    }()
    
    let moreButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_more_white"), for: .normal)
        return button
    }()
    
    let pageViewController: UIPageViewController = {
        let vc = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .vertical,
            options: [UIPageViewController.OptionsKey.interPageSpacing: 0]
        )
        return vc
    }()
    
    // MARK: - 初始化
    init() {}
    
    // MARK: - UI Setup
    func setupUI(in viewController: UIViewController, 
                 showCustomNavBar: Bool,
                 hideNavBackButton: Bool,
                 needsTabBarOffset: Bool,
                 backAction: Selector,
                 searchAction: Selector,
                 moreAction: Selector) {
        let view = viewController.view!
        view.backgroundColor = .black
        
        // 添加页面控制器
        viewController.addChild(pageViewController)
        view.addSubview(pageViewController.view)
        pageViewController.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        pageViewController.didMove(toParent: viewController)
        
        // 添加自定义导航栏（根据 showCustomNavBar 决定）
        view.addSubview(navBar)
        navBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.height.equalTo(showCustomNavBar ? 44 : 0) // 如果不展示导航栏，则高度设为 0
        }
        navBar.isHidden = !showCustomNavBar

        guard showCustomNavBar else { return } // 不需要继续添加按钮

        // 添加导航栏按钮
        navBar.addSubview(backButton)
        navBar.addSubview(searchButton)
        navBar.addSubview(moreButton)
        
        // 设置按钮事件
        backButton.addTarget(viewController, action: backAction, for: .touchUpInside)
        searchButton.addTarget(viewController, action: searchAction, for: .touchUpInside)
        moreButton.addTarget(viewController, action: moreAction, for: .touchUpInside)

        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }

        moreButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }

        searchButton.snp.makeConstraints { make in
            make.right.equalTo(moreButton.snp.left).offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }

        // 根据配置隐藏返回按钮
        backButton.isHidden = hideNavBackButton
 
        // 显示右上角“更多”按钮（即三个点按钮），搜索按钮在其左侧
        let shouldShowMoreButton = true
        moreButton.isHidden = !shouldShowMoreButton

        if !shouldShowMoreButton {
            // 当隐藏更多按钮时，将搜索按钮贴右侧
            searchButton.snp.remakeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.height.equalTo(32)
            }
        }
    }
} 