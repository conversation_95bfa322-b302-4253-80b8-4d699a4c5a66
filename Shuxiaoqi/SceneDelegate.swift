//
//  SceneDelegate.swift
//  Shuxiaoqi
//
//  Created by steamed_b on 2025/3/18.
//

import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate, UIAdaptivePresentationControllerDelegate {

    var window: UIWindow?
    
    // 防重复弹窗标记
    private var isShowingVideoAlert = false

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        // 确保只创建一次窗口
        window = UIWindow(windowScene: windowScene)

        // 立即显示窗口，减少启动页显示时间
        window?.makeKeyAndVisible()

        // 延后执行界面创建，让启动页快速消失
        DispatchQueue.main.async {
            self.setupInitialInterface()
        }
    }

    /// 设置初始界面
    private func setupInitialInterface() {
        // 检查是否首次安装
        if UserDefaults.standard.bool(forKey: "hasLaunchedBefore") {
            // 非首次安装，检查用户协议
            checkUserAgreementAndSetupInterface()
        } else {
            // 首次安装，显示引导页
            let guideVC = GuideViewController()
            window?.rootViewController = guideVC

            // 标记已经启动过应用
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
            UserDefaults.standard.synchronize()
        }
    }

    /// 检查用户协议并设置界面
    private func checkUserAgreementAndSetupInterface() {
        // 先设置主界面
        setupMainInterface()

        // 延迟一点时间确保界面已经完全加载，然后检查用户协议
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            UserAgreementManager.shared.checkAndShowAgreementIfNeeded { [weak self] accepted in
                if !accepted {
                    // 用户不同意协议，应用将退出
                    print("[SceneDelegate] 用户不同意用户协议，应用即将退出")
                }
            }
        }
    }
    
    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        if let url = URLContexts.first?.url {
            WXApi.handleOpen(url, delegate: UIApplication.shared.delegate as? WXApiDelegate)
        }
    }
    
    // 设置主界面的方法
    func setupMainInterface() {
        // 创建自定义 TabBar 控制器
        let tabBarController = CustomTabBarController()

        // 首页 - 包装在自定义导航控制器中
        let homeVC = HomeViewController()
        homeVC.title = "首页"
        homeVC.isTabBarRootViewController = true  // 标记为主要标签页面
        let homeNav = HiddenNavController(rootViewController: homeVC)

        // 朋友 - 使用通用控制器或项目中已有的控制器
        let friendsVC = FriendViewController()
        friendsVC.title = "朋友"
        friendsVC.isTabBarRootViewController = true
        let friendsNav = HiddenNavController(rootViewController: friendsVC)

        // 中间的发布按钮对应的控制器
        let addVC = UIViewController()
        addVC.title = "发布"
        let addNav = HiddenNavController(rootViewController: addVC)

        // 消息
        let messageVC = MessageViewController()
        messageVC.title = "消息"
        messageVC.isTabBarRootViewController = true
        let messageNav = HiddenNavController(rootViewController: messageVC)

        // 我的
        let meVC = MeViewController()
        meVC.title = "我的"
        meVC.isTabBarRootViewController = true
        let meNav = HiddenNavController(rootViewController: meVC)

        // 设置视图控制器
        tabBarController.setViewControllers([homeNav, friendsNav, addNav, messageNav, meNav], animated: false)

        // 设置窗口的根视图控制器
        window?.rootViewController = tabBarController

        // 确保TabBar显示并选择首页
        tabBarController.showTabBar()
        tabBarController.selectTab(at: 0) // 确保选择第一个标签
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
        print("[SceneDelegate] sceneDidBecomeActive 被调用 - \(Date())")
        checkClipboardForAddFriendCommand()
        checkClipboardForVideoCommand()
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }

    // MARK: - Clipboard Command Handling
    private func checkClipboardForAddFriendCommand() {
        // 口令前缀
        let prefix = "复制到【树小柒】APP添加"

        guard let clipboardText = UIPasteboard.general.string,
              clipboardText.hasPrefix(prefix) else { return }

        // 防止重复弹窗
        if UserDefaults.standard.string(forKey: "lastProcessedClipboard") == clipboardText {
            return
        }

        let userId = clipboardText.replacingOccurrences(of: prefix, with: "").trimmingCharacters(in: .whitespacesAndNewlines)
        guard !userId.isEmpty else { return }

        // 保存已处理口令
        UserDefaults.standard.set(clipboardText, forKey: "lastProcessedClipboard")

        // 构建提示弹窗
        let alert = UIAlertController(title: "添加好友", message: "检测到好友口令，是否查看用户 \(userId)？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alert.addAction(UIAlertAction(title: "查看", style: .default) { _ in
            // 跳转到个人主页
            let personalVC = PersonalHomepageViewController()
            personalVC.userId = userId
            // 寻找当前最顶层视图控制器
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                if let nav = topVC.navigationController {
                    nav.pushViewController(personalVC, animated: true)
                } else {
                    topVC.present(personalVC, animated: true)
                }
            }
        })

        // 显示弹窗
        DispatchQueue.main.async {
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                topVC.present(alert, animated: true)
            }
        }
    }

    // MARK: - Video Command Handling
    private func checkClipboardForVideoCommand() {
        print("[SceneDelegate] 开始检查剪贴板视频口令")
        
        guard let clipboardText = UIPasteboard.general.string else {
            print("[SceneDelegate] 剪贴板为空")
            return
        }
        
        print("[SceneDelegate] 剪贴板内容: \(clipboardText)")
        print("[SceneDelegate] 剪贴板内容长度: \(clipboardText.count)")
        
        // 检查视频分享口令格式：复制打开【树小柒】APP，看看《视频标题》
        let videoPrefix = "复制打开【树小柒】APP，看看《"
        if clipboardText.hasPrefix(videoPrefix) {
            print("[SceneDelegate] 检测到视频分享口令")
            // 检查是否包含视频链接
            if clipboardText.contains("yigou.gzyoushu.com") && clipboardText.contains("videoDetail") {
                print("[SceneDelegate] 口令包含视频链接")
            } else {
                print("[SceneDelegate] 口令不包含视频链接，可能需要从其他地方获取视频ID")
            }
            handleVideoShareCommand(clipboardText)
            return
        }
        
        // 检查是否包含视频分享链接
        if clipboardText.contains("yigou.gzyoushu.com") && clipboardText.contains("videoDetail") {
            print("[SceneDelegate] 检测到视频分享链接")
            handleVideoLinkCommand(clipboardText)
            return
        }
        
        print("[SceneDelegate] 未检测到视频相关内容")
    }
    
    private func handleVideoShareCommand(_ clipboardText: String) {
        print("[SceneDelegate] 处理视频分享口令")
        
        // 防重复弹窗检查
        if isShowingVideoAlert {
            print("[SceneDelegate] 已有视频弹窗显示中，跳过")
            return
        }
        
        // 提取视频标题
        let videoPrefix = "复制打开【树小柒】APP，看看《"
        let videoSuffix = "》"
        
        print("[SceneDelegate] 尝试提取视频标题")
        
        guard clipboardText.hasPrefix(videoPrefix),
              let titleStartIndex = clipboardText.range(of: videoPrefix)?.upperBound,
              let titleEndIndex = clipboardText.range(of: videoSuffix, range: titleStartIndex..<clipboardText.endIndex)?.lowerBound else {
            print("[SceneDelegate] 无法提取视频标题")
            return
        }
        
        let videoTitle = String(clipboardText[titleStartIndex..<titleEndIndex])
        guard !videoTitle.isEmpty else { 
            print("[SceneDelegate] 视频标题为空")
            return 
        }
        
        print("[SceneDelegate] 提取到视频标题: \(videoTitle)")
        
        // 设置弹窗显示标记
        isShowingVideoAlert = true
        
        // 构建提示弹窗
        let alert = UIAlertController(title: "观看视频", message: "检测到视频口令，是否观看《\(videoTitle)》？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            self.isShowingVideoAlert = false
        })
        alert.addAction(UIAlertAction(title: "观看", style: .default) { _ in
            print("[SceneDelegate] 用户选择观看视频")
            self.isShowingVideoAlert = false
            // 清理剪贴板，避免重复触发
            UIPasteboard.general.string = ""
            // 从剪贴板文本中查找视频链接并提取ID
            self.extractVideoIdAndPlay(from: clipboardText)
        })
        
        // 显示弹窗
        print("[SceneDelegate] 准备显示视频观看确认弹窗")
        DispatchQueue.main.async {
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                print("[SceneDelegate] 显示视频观看确认弹窗")
                topVC.present(alert, animated: true)
            } else {
                print("[SceneDelegate] 无法找到顶层视图控制器")
                self.isShowingVideoAlert = false
            }
        }
    }
    
    private func handleVideoLinkCommand(_ clipboardText: String) {
        // 防重复弹窗检查
        if isShowingVideoAlert {
            print("[SceneDelegate] 已有视频弹窗显示中，跳过链接处理")
            return
        }
        
        // 设置弹窗显示标记
        isShowingVideoAlert = true
        
        // 构建提示弹窗
        let alert = UIAlertController(title: "观看视频", message: "检测到视频链接，是否观看这个视频？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            self.isShowingVideoAlert = false
        })
        alert.addAction(UIAlertAction(title: "观看", style: .default) { _ in
            self.isShowingVideoAlert = false
            // 清理剪贴板，避免重复触发
            UIPasteboard.general.string = ""
            self.extractVideoIdAndPlay(from: clipboardText)
        })
        
        // 显示弹窗
        DispatchQueue.main.async {
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                topVC.present(alert, animated: true)
            } else {
                self.isShowingVideoAlert = false
            }
        }
    }
    
    private func extractVideoIdAndPlay(from text: String) {
        // 从文本中提取视频ID
        // 链接格式：https://yigou.gzyoushu.com/#/sxqVideo/videoDetail/videoDetail?id={videoId}
        guard let range = text.range(of: "videoDetail?id="),
              let idStartIndex = text.range(of: "videoDetail?id=")?.upperBound else {
            print("无法从文本中提取视频ID")
            return
        }
        
        // 提取ID部分（可能包含其他参数）
        let remainingText = String(text[idStartIndex...])
        let videoIdString: String
        
        if let ampersandIndex = remainingText.firstIndex(of: "&") {
            videoIdString = String(remainingText[..<ampersandIndex])
        } else if let spaceIndex = remainingText.firstIndex(of: " ") {
            videoIdString = String(remainingText[..<spaceIndex])
        } else if let newlineIndex = remainingText.firstIndex(of: "\n") {
            videoIdString = String(remainingText[..<newlineIndex])
        } else {
            videoIdString = remainingText.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        guard let videoId = Int(videoIdString) else {
            print("无法解析视频ID: \(videoIdString)")
            return
        }
        
        print("提取到视频ID: \(videoId)")
        
        // 使用APIManager获取视频详情
        APIManager.shared.getVideoDetail(videoId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let videoData = response.data {
                        print("获取视频详情成功: \(videoData.worksTitle ?? "未知标题")")
                        self?.playVideo(videoData)
                    } else {
                        print("视频详情数据为空")
                        self?.showVideoNotFoundAlert()
                    }
                case .failure(let error):
                    print("获取视频详情失败: \(error.localizedDescription)")
                    self?.showVideoNotFoundAlert()
                }
            }
        }
    }
    
    private func playVideo(_ videoItem: VideoItem) {
        // 创建视频播放控制器，使用模态弹出时不需要TabBar偏移
        let videoVC = VideoDisplayCenterViewController(singleVideoItem: videoItem, 
                                                      hideNavBackButton: false, 
                                                      showCustomNavBar: true, 
                                                      needsTabBarOffset: false)
        
        // 使用模态弹出方式，确保TabBar正确隐藏
        let navController = UINavigationController(rootViewController: videoVC)
        navController.modalPresentationStyle = .fullScreen
        
        // 自定义导航控制器的dismiss行为
        navController.presentationController?.delegate = self
        
        // 寻找当前最顶层视图控制器并模态弹出
        if let topVC = self.topViewController(base: self.window?.rootViewController) {
            topVC.present(navController, animated: true)
        }
    }
    
    private func showVideoNotFoundAlert() {
        let alert = UIAlertController(title: "视频不存在", message: "该视频可能已被删除或不存在", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default, handler: nil))
        
        DispatchQueue.main.async {
            if let topVC = self.topViewController(base: self.window?.rootViewController) {
                topVC.present(alert, animated: true)
            }
        }
    }

    private func topViewController(base: UIViewController?) -> UIViewController? {
        if let nav = base as? UINavigationController {
            return topViewController(base: nav.visibleViewController)
        } else if let tab = base as? UITabBarController,
                  let selected = tab.selectedViewController {
            return topViewController(base: selected)
        } else if let presented = base?.presentedViewController {
            return topViewController(base: presented)
        }
        return base
    }

}

