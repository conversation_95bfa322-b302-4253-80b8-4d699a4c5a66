import UIKit

/// 用户协议确认弹窗组件
/// 当用户点击"不同意"时显示的二次确认弹窗
final class UserAgreementConfirmPopupView: UIView, UITextViewDelegate {
    
    // MARK: - UI Elements
    private let backgroundView = UIView()
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private var messageTextView: UITextView!
    private let buttonStackView = UIStackView()
    private let agreeButton = UIButton(type: .system)
    private let exitButton = UIButton(type: .system)
    
    // MARK: - Properties
    private var onAgree: (() -> Void)?
    private var onExit: (() -> Void)?
    
    // MARK: - Initializer
    private init(onAgree: @escaping () -> Void, onExit: @escaping () -> Void) {
        super.init(frame: .zero)
        self.onAgree = onAgree
        self.onExit = onExit
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public API
    /// 显示用户协议确认弹窗
    /// - Parameters:
    ///   - onAgree: 用户点击"同意并继续"的回调
    ///   - onExit: 用户点击"放弃使用"的回调
    static func show(onAgree: @escaping () -> Void, onExit: @escaping () -> Void) {
        guard let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
        
        let popup = UserAgreementConfirmPopupView(onAgree: onAgree, onExit: onExit)
        popup.frame = keyWindow.bounds
        popup.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        keyWindow.addSubview(popup)
        
        // 添加动画效果
        popup.alpha = 0
        popup.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [], animations: {
            popup.alpha = 1
            popup.containerView.transform = .identity
        }, completion: nil)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 背景遮罩
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        addSubview(backgroundView)
        
        // 容器视图
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 16
        containerView.clipsToBounds = true
        addSubview(containerView)
        
        // 标题
        titleLabel.text = "温馨提示"
        titleLabel.font = .boldSystemFont(ofSize: 18)
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        containerView.addSubview(titleLabel)
        
        // 消息内容 - 使用 UITextView 支持富文本和点击
        messageTextView = UITextView()
        messageTextView.font = .systemFont(ofSize: 15)
        messageTextView.textColor = UIColor(hex: "#666666")
        messageTextView.backgroundColor = .clear
        messageTextView.isEditable = false
        messageTextView.isScrollEnabled = false
        messageTextView.textContainer.lineFragmentPadding = 0
        messageTextView.textContainerInset = .zero
        messageTextView.delegate = self
        messageTextView.textAlignment = .center
        // 设置链接颜色
        messageTextView.linkTextAttributes = [
            .foregroundColor: UIColor(hex: "#FF8F1F"),
            .underlineStyle: NSUnderlineStyle().rawValue
        ]
        messageTextView.attributedText = getAttributedConfirmMessage()
        containerView.addSubview(messageTextView)
        
        // 按钮容器
        buttonStackView.axis = .horizontal
        buttonStackView.distribution = .fillEqually
        buttonStackView.spacing = 12
        containerView.addSubview(buttonStackView)
        
        // 放弃使用按钮
        exitButton.setTitle("放弃使用", for: .normal)
        exitButton.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        exitButton.titleLabel?.font = .systemFont(ofSize: 16)
        exitButton.backgroundColor = UIColor(hex: "#F5F5F5")
        exitButton.layer.cornerRadius = 22
        exitButton.addTarget(self, action: #selector(exitButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(exitButton)
        
        // 同意并继续按钮
        agreeButton.setTitle("同意并继续", for: .normal)
        agreeButton.setTitleColor(.white, for: .normal)
        agreeButton.titleLabel?.font = .boldSystemFont(ofSize: 16)
        agreeButton.backgroundColor = UIColor(hex: "#FF8F1F")
        agreeButton.layer.cornerRadius = 22
        agreeButton.addTarget(self, action: #selector(agreeButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(agreeButton)
    }
    
    private func setupConstraints() {
        // 禁用自动布局转换
        [backgroundView, containerView, titleLabel, messageTextView, buttonStackView].forEach {
            $0.translatesAutoresizingMaskIntoConstraints = false
        }
        
        NSLayoutConstraint.activate([
            // 背景视图
            backgroundView.topAnchor.constraint(equalTo: topAnchor),
            backgroundView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // 容器视图
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.leadingAnchor.constraint(greaterThanOrEqualTo: leadingAnchor, constant: 40),
            containerView.trailingAnchor.constraint(lessThanOrEqualTo: trailingAnchor, constant: -40),
            containerView.widthAnchor.constraint(equalToConstant: 300),
            
            // 标题
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 24),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 消息内容
            messageTextView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            messageTextView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            messageTextView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),

            // 按钮容器
            buttonStackView.topAnchor.constraint(equalTo: messageTextView.bottomAnchor, constant: 24),
            buttonStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            buttonStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            buttonStackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20),
            buttonStackView.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    // MARK: - Actions
    @objc private func agreeButtonTapped() {
        dismissWithAnimation {
            self.onAgree?()
        }
    }
    
    @objc private func exitButtonTapped() {
        dismissWithAnimation {
            self.onExit?()
        }
    }
    
    private func dismissWithAnimation(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.25, animations: {
            self.alpha = 0
            self.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }) { _ in
            self.removeFromSuperview()
            completion()
        }
    }
    
    // MARK: - Content
    private func getAttributedConfirmMessage() -> NSAttributedString {
        let text = """
如果您不同意《用户协议》和《隐私政策》，很遗憾我们将无法为您提供服务。

您需要同意以上协议后，才能使用树小柒。

我们将严格按照相关法律法规要求，坚决保障您的个人隐私和信息安全。
"""

        let attributedString = NSMutableAttributedString(string: text)

        // 设置默认样式
        let defaultAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 15),
            .foregroundColor: UIColor(hex: "#666666")
        ]
        attributedString.addAttributes(defaultAttributes, range: NSRange(location: 0, length: text.count))

        // 设置《用户协议》的样式和链接
        let userAgreementRange = (text as NSString).range(of: "《用户协议》")
        if userAgreementRange.location != NSNotFound {
            attributedString.addAttributes([
                .foregroundColor: UIColor(hex: "#FF8F1F"),
                .link: "user_agreement"
            ], range: userAgreementRange)
        }

        // 设置《隐私政策》的样式和链接
        let privacyPolicyRange = (text as NSString).range(of: "《隐私政策》")
        if privacyPolicyRange.location != NSNotFound {
            attributedString.addAttributes([
                .foregroundColor: UIColor(hex: "#FF8F1F"),
                .link: "privacy_policy"
            ], range: privacyPolicyRange)
        }

        return attributedString
    }

    // MARK: - UITextViewDelegate
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        let urlString = URL.absoluteString

        if urlString == "user_agreement" {
            openUserAgreement()
            return false
        } else if urlString == "privacy_policy" {
            openPrivacyPolicy()
            return false
        }

        return true
    }

    private func openUserAgreement() {
        print("点击了用户协议链接")
        // 找到当前的视图控制器
        if let viewController = findParentViewController() {
            print("找到了视图控制器: \(viewController)")
            SimpleWebViewController.showUserAgreement(from: viewController)
        } else {
            print("未找到视图控制器")
            // 备用方案：使用keyWindow的rootViewController
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
               let rootVC = keyWindow.rootViewController {
                print("使用keyWindow的rootViewController")
                SimpleWebViewController.showUserAgreement(from: rootVC)
            }
        }
    }

    private func openPrivacyPolicy() {
        print("点击了隐私政策链接")
        // 找到当前的视图控制器
        if let viewController = findParentViewController() {
            print("找到了视图控制器: \(viewController)")
            SimpleWebViewController.showPrivacyPolicy(from: viewController)
        } else {
            print("未找到视图控制器")
            // 备用方案：使用keyWindow的rootViewController
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
               let rootVC = keyWindow.rootViewController {
                print("使用keyWindow的rootViewController")
                SimpleWebViewController.showPrivacyPolicy(from: rootVC)
            }
        }
    }

    private func findParentViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while responder != nil {
            if let viewController = responder as? UIViewController {
                return viewController
            }
            responder = responder?.next
        }
        return nil
    }
}
