# 用户协议功能说明

## 功能概述

本功能实现了应用启动时的用户协议和隐私政策确认弹窗，确保用户在使用应用前同意相关协议。

## 功能特点

1. **启动时自动检查**：应用启动时自动检查用户是否已同意协议
2. **两级确认机制**：
   - 第一个弹窗：显示详细的协议内容，用户可选择"同意并继续"或"不同意"
   - 第二个弹窗：当用户选择"不同意"时显示，提供"同意并继续"或"放弃使用"选项
3. **可点击链接**：协议文本中的《用户协议》和《隐私政策》为橙色可点击链接，点击可跳转到对应页面
4. **本地存储**：用户同意状态保存在本地，只需同意一次
5. **优雅退出**：用户选择"放弃使用"时应用会优雅退出

## 文件结构

```
Shuxiaoqi/
├── Components/
│   ├── UserAgreementPopupView.swift          # 主要用户协议弹窗
│   └── UserAgreementConfirmPopupView.swift   # 确认弹窗（不同意时显示）
├── Managers/
│   └── UserAgreementManager.swift            # 用户协议管理器
├── SceneDelegate.swift                       # 修改了启动流程
└── Common/Guide/GuideViewController.swift    # 修改了引导页完成流程
```

## 核心组件

### 1. UserAgreementPopupView
- 显示详细的用户协议和隐私政策内容
- 包含滚动视图以显示长文本
- 使用UITextView支持富文本，《用户协议》和《隐私政策》为橙色可点击链接
- 提供"同意并继续"和"不同意"两个按钮

### 2. UserAgreementConfirmPopupView
- 当用户点击"不同意"时显示的二次确认弹窗
- 同样支持《用户协议》和《隐私政策》的可点击链接
- 提供"同意并继续"和"放弃使用"两个选项
- 选择"放弃使用"会退出应用

### 3. UserAgreementManager
- 单例模式管理用户协议状态
- 提供检查和显示协议弹窗的方法
- 处理用户协议状态的本地存储

## 使用方法

### 自动启动检查
应用启动时会自动检查用户协议状态：

```swift
// 在 SceneDelegate.swift 中
UserAgreementManager.shared.checkAndShowAgreementIfNeeded { accepted in
    if !accepted {
        // 用户不同意协议，应用将退出
        print("用户不同意用户协议，应用即将退出")
    }
}
```

### 手动显示协议弹窗
```swift
UserAgreementManager.shared.showUserAgreementPopup { accepted in
    if accepted {
        print("用户同意了协议")
    } else {
        print("用户不同意协议")
    }
}
```

### 检查协议状态
```swift
let hasAccepted = UserAgreementManager.shared.hasUserAcceptedAgreement
```

### 重置协议状态（测试用）
```swift
UserAgreementManager.shared.resetUserAgreementStatus()
```

## 测试功能

在设置页面添加了两个测试选项：

1. **重置用户协议(测试)**：清除本地保存的用户协议同意状态
2. **测试用户协议弹窗**：直接显示用户协议弹窗进行测试

## 协议内容

弹窗中显示的协议内容包括：

1. 欢迎使用树小柒的说明
2. 用户协议和隐私政策的重要性
3. 权限使用说明：
   - 摄像头、相册、麦克风权限（用于拍照、录制功能）
   - 地理位置权限（用于内容推荐）
4. 隐私保护承诺

## 技术实现

### 本地存储
使用 UserDefaults 存储用户协议同意状态：
```swift
private let userAgreementAcceptedKey = "UserAgreementAccepted"
```

### 富文本链接
使用 UITextView 和 NSAttributedString 实现可点击的协议链接：
- 《用户协议》和《隐私政策》显示为橙色（#FF8F1F）
- 实现 UITextViewDelegate 处理链接点击事件
- 点击链接跳转到对应的WebViewController页面

### 弹窗动画
使用 UIView 动画实现弹窗的显示和隐藏效果：
- 淡入淡出效果
- 缩放动画
- 弹性动画效果

### 响应式布局
使用 Auto Layout 确保弹窗在不同屏幕尺寸下正确显示。

## 注意事项

1. **首次启动**：首次安装应用时，会先显示引导页，然后显示用户协议弹窗
2. **非首次启动**：非首次启动时，如果用户未同意协议，会直接显示协议弹窗
3. **网络链接**：协议中的《用户协议》和《隐私政策》链接到实际的网页
4. **测试模式**：测试功能仅在开发阶段使用，正式发布时应移除

## 自定义配置

### 修改协议内容
在 `UserAgreementPopupView.swift` 的 `getAgreementText()` 方法中修改协议文本。

### 修改协议链接
在相关文件中修改协议链接：
- 用户协议：`https://gzyoushu.com/privacy/ysh-docuser.htm`
- 隐私政策：`https://gzyoushu.com/privacy/ys-doctset.html`

### 修改样式
可以在各个组件中修改颜色、字体、布局等样式设置。
